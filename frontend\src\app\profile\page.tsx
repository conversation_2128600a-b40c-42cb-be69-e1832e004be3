"use client"

import { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { Navigation } from '@/components/Navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  User, 
  Mail, 
  Phone, 
  MapPin, 
  Calendar, 
  Edit, 
  Save, 
  X,
  Shield,
  School,
  Truck,
  GraduationCap,
  Camera,
  Settings,
  Bell,
  Lock
} from 'lucide-react'
import { toast } from 'sonner'

export default function ProfilePage() {
  const { user, profile } = useAuth()
  const [isEditing, setIsEditing] = useState(false)
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState({
    full_name: '',
    phone: '',
    address: '',
    bio: '',
    birth_date: '',
    gender: ''
  })

  // التحقق من تسجيل الدخول
  useEffect(() => {
    if (!user) {
      window.location.href = '/auth'
      return
    }
  }, [user])

  // تحميل بيانات الملف الشخصي
  useEffect(() => {
    if (profile) {
      setFormData({
        full_name: profile.full_name || '',
        phone: profile.phone || '',
        address: profile.address || '',
        bio: profile.bio || '',
        birth_date: profile.birth_date || '',
        gender: profile.gender || ''
      })
    }
  }, [profile])

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'admin':
        return <Shield className="h-5 w-5 text-red-600" />
      case 'school':
        return <School className="h-5 w-5 text-blue-600" />
      case 'delivery':
        return <Truck className="h-5 w-5 text-green-600" />
      case 'student':
        return <GraduationCap className="h-5 w-5 text-purple-600" />
      default:
        return <User className="h-5 w-5 text-gray-600" />
    }
  }

  const getRoleLabel = (role: string) => {
    switch (role) {
      case 'admin':
        return 'مدير النظام'
      case 'school':
        return 'مدرسة'
      case 'delivery':
        return 'شريك توصيل'
      case 'student':
        return 'طالب'
      default:
        return 'مستخدم'
    }
  }

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word[0])
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  const handleSave = async () => {
    setLoading(true)
    try {
      // محاكاة حفظ البيانات
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      toast.success('تم حفظ التغييرات بنجاح')
      setIsEditing(false)
    } catch (error) {
      toast.error('فشل في حفظ التغييرات')
    } finally {
      setLoading(false)
    }
  }

  const handleCancel = () => {
    if (profile) {
      setFormData({
        full_name: profile.full_name || '',
        phone: profile.phone || '',
        address: profile.address || '',
        bio: profile.bio || '',
        birth_date: profile.birth_date || '',
        gender: profile.gender || ''
      })
    }
    setIsEditing(false)
  }

  if (!user || !profile) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <Navigation />
        <div className="flex items-center justify-center min-h-[60vh]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600 dark:text-gray-400">جاري التحميل...</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <Navigation />
      
      <main className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white arabic-text">
            الملف الشخصي 👤
          </h1>
          <p className="text-gray-600 dark:text-gray-300 mt-2 arabic-text">
            إدارة معلوماتك الشخصية وإعدادات الحساب
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Profile Card */}
          <div className="lg:col-span-1">
            <Card>
              <CardHeader className="text-center">
                <div className="relative mx-auto mb-4">
                  <Avatar className="h-24 w-24 mx-auto">
                    <AvatarImage src={profile.avatar_url} />
                    <AvatarFallback className="bg-blue-600 text-white text-xl">
                      {getInitials(profile.full_name)}
                    </AvatarFallback>
                  </Avatar>
                  <Button
                    size="sm"
                    variant="outline"
                    className="absolute -bottom-2 -right-2 h-8 w-8 rounded-full p-0"
                  >
                    <Camera className="h-4 w-4" />
                  </Button>
                </div>
                <CardTitle className="arabic-text">{profile.full_name}</CardTitle>
                <CardDescription className="flex items-center justify-center gap-2">
                  {getRoleIcon(profile.role)}
                  <span>{getRoleLabel(profile.role)}</span>
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                  <Mail className="h-4 w-4" />
                  <span>{user.email}</span>
                </div>
                {profile.phone && (
                  <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                    <Phone className="h-4 w-4" />
                    <span>{profile.phone}</span>
                  </div>
                )}
                {profile.address && (
                  <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                    <MapPin className="h-4 w-4" />
                    <span>{profile.address}</span>
                  </div>
                )}
                <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                  <Calendar className="h-4 w-4" />
                  <span>انضم في {new Date(profile.created_at).toLocaleDateString('ar-SA')}</span>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-2">
            <Tabs defaultValue="personal" className="space-y-6">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="personal" className="arabic-text">المعلومات الشخصية</TabsTrigger>
                <TabsTrigger value="settings" className="arabic-text">الإعدادات</TabsTrigger>
                <TabsTrigger value="notifications" className="arabic-text">الإشعارات</TabsTrigger>
              </TabsList>

              {/* Personal Information Tab */}
              <TabsContent value="personal">
                <Card>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div>
                        <CardTitle className="arabic-text">المعلومات الشخصية</CardTitle>
                        <CardDescription>
                          تحديث معلوماتك الشخصية والتفاصيل
                        </CardDescription>
                      </div>
                      {!isEditing ? (
                        <Button onClick={() => setIsEditing(true)} variant="outline" size="sm">
                          <Edit className="h-4 w-4 mr-2" />
                          تحرير
                        </Button>
                      ) : (
                        <div className="flex gap-2">
                          <Button onClick={handleSave} size="sm" disabled={loading}>
                            <Save className="h-4 w-4 mr-2" />
                            {loading ? 'جاري الحفظ...' : 'حفظ'}
                          </Button>
                          <Button onClick={handleCancel} variant="outline" size="sm">
                            <X className="h-4 w-4 mr-2" />
                            إلغاء
                          </Button>
                        </div>
                      )}
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="full_name">الاسم الكامل</Label>
                        <Input
                          id="full_name"
                          value={formData.full_name}
                          onChange={(e) => setFormData({...formData, full_name: e.target.value})}
                          disabled={!isEditing}
                          className="arabic-text"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="phone">رقم الهاتف</Label>
                        <Input
                          id="phone"
                          value={formData.phone}
                          onChange={(e) => setFormData({...formData, phone: e.target.value})}
                          disabled={!isEditing}
                          placeholder="+212 6XX-XXXXXX"
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="address">العنوان</Label>
                      <Input
                        id="address"
                        value={formData.address}
                        onChange={(e) => setFormData({...formData, address: e.target.value})}
                        disabled={!isEditing}
                        className="arabic-text"
                        placeholder="المدينة، المغرب"
                      />
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="birth_date">تاريخ الميلاد</Label>
                        <Input
                          id="birth_date"
                          type="date"
                          value={formData.birth_date}
                          onChange={(e) => setFormData({...formData, birth_date: e.target.value})}
                          disabled={!isEditing}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="gender">الجنس</Label>
                        <Select
                          value={formData.gender}
                          onValueChange={(value) => setFormData({...formData, gender: value})}
                          disabled={!isEditing}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="اختر الجنس" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="male">ذكر</SelectItem>
                            <SelectItem value="female">أنثى</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="bio">نبذة شخصية</Label>
                      <Textarea
                        id="bio"
                        value={formData.bio}
                        onChange={(e) => setFormData({...formData, bio: e.target.value})}
                        disabled={!isEditing}
                        className="arabic-text"
                        placeholder="اكتب نبذة مختصرة عن نفسك..."
                        rows={4}
                      />
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Settings Tab */}
              <TabsContent value="settings">
                <Card>
                  <CardHeader>
                    <CardTitle className="arabic-text flex items-center gap-2">
                      <Settings className="h-5 w-5" />
                      إعدادات الحساب
                    </CardTitle>
                    <CardDescription>
                      إدارة إعدادات الأمان والخصوصية
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="flex items-center justify-between p-4 border rounded-lg">
                      <div>
                        <h3 className="font-medium arabic-text">تغيير كلمة المرور</h3>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          تحديث كلمة المرور لحسابك
                        </p>
                      </div>
                      <Button variant="outline" size="sm">
                        <Lock className="h-4 w-4 mr-2" />
                        تغيير
                      </Button>
                    </div>

                    <div className="flex items-center justify-between p-4 border rounded-lg">
                      <div>
                        <h3 className="font-medium arabic-text">المصادقة الثنائية</h3>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          تفعيل طبقة حماية إضافية لحسابك
                        </p>
                      </div>
                      <Badge variant="secondary">غير مفعل</Badge>
                    </div>

                    <div className="flex items-center justify-between p-4 border rounded-lg">
                      <div>
                        <h3 className="font-medium arabic-text">حذف الحساب</h3>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          حذف حسابك وجميع البيانات المرتبطة به
                        </p>
                      </div>
                      <Button variant="destructive" size="sm">
                        حذف الحساب
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Notifications Tab */}
              <TabsContent value="notifications">
                <Card>
                  <CardHeader>
                    <CardTitle className="arabic-text flex items-center gap-2">
                      <Bell className="h-5 w-5" />
                      إعدادات الإشعارات
                    </CardTitle>
                    <CardDescription>
                      تخصيص الإشعارات التي تريد تلقيها
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className="font-medium arabic-text">إشعارات البريد الإلكتروني</h3>
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            تلقي الإشعارات عبر البريد الإلكتروني
                          </p>
                        </div>
                        <input type="checkbox" className="toggle" defaultChecked />
                      </div>

                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className="font-medium arabic-text">إشعارات الطلبات</h3>
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            تحديثات حول حالة طلباتك
                          </p>
                        </div>
                        <input type="checkbox" className="toggle" defaultChecked />
                      </div>

                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className="font-medium arabic-text">إشعارات التسويق</h3>
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            عروض خاصة ومنتجات جديدة
                          </p>
                        </div>
                        <input type="checkbox" className="toggle" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </main>
    </div>
  )
}
