import { NextRequest, NextResponse } from 'next/server'
import { MockDataManager, MockProduct } from '@/lib/mockData'

// GET - جلب جميع المنتجات
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const category = searchParams.get('category')
    const available = searchParams.get('available')
    const limit = searchParams.get('limit')
    const offset = searchParams.get('offset')

    // جلب البيانات الوهمية
    let products = MockDataManager.getProducts()

    // تطبيق الفلاتر
    if (category && category !== 'all') {
      products = products.filter(product => product.category === category)
    }

    if (available === 'true') {
      products = products.filter(product => product.is_available === true)
    } else if (available === 'false') {
      products = products.filter(product => product.is_available === false)
    }

    // ترتيب حسب تاريخ الإنشاء
    products.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())

    // تطبيق التصفح
    const limitNum = limit ? parseInt(limit) : products.length
    const offsetNum = offset ? parseInt(offset) : 0
    
    const paginatedProducts = products.slice(offsetNum, offsetNum + limitNum)

    return NextResponse.json({ 
      products: paginatedProducts,
      total: products.length
    })
  } catch (error) {
    console.error('Unexpected error:', error)
    return NextResponse.json(
      { error: 'خطأ غير متوقع' },
      { status: 500 }
    )
  }
}

// POST - إضافة منتج جديد
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      name,
      description,
      category,
      price,
      rental_price,
      colors,
      sizes,
      images,
      stock_quantity,
      is_available,
      features,
      specifications
    } = body

    // التحقق من البيانات المطلوبة
    if (!name || !description || !category || !price) {
      return NextResponse.json(
        { error: 'البيانات المطلوبة مفقودة' },
        { status: 400 }
      )
    }

    // جلب المنتجات الحالية
    const products = MockDataManager.getProducts()

    // إنشاء المنتج الجديد
    const newProduct: MockProduct = {
      id: MockDataManager.generateId(),
      name,
      description,
      category,
      price: parseFloat(price),
      rental_price: rental_price ? parseFloat(rental_price) : undefined,
      colors: colors || [],
      sizes: sizes || [],
      images: images || [],
      stock_quantity: parseInt(stock_quantity) || 0,
      is_available: is_available ?? true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      features: features || [],
      specifications: specifications || {}
    }

    // حفظ المنتج
    products.push(newProduct)
    MockDataManager.saveProducts(products)

    return NextResponse.json({ 
      message: 'تم إضافة المنتج بنجاح',
      product: newProduct 
    }, { status: 201 })

  } catch (error) {
    console.error('Unexpected error:', error)
    return NextResponse.json(
      { error: 'خطأ غير متوقع' },
      { status: 500 }
    )
  }
}
